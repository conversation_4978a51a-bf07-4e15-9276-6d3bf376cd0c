// 游戏状态管理
class GameState {
    constructor() {
        this.isPlaying = false;
        this.remainingChances = 3;
        this.hitCount = 0;
        this.isDragging = false;
        this.dragStart = { x: 0, y: 0 };
        this.dragEnd = { x: 0, y: 0 };
        this.projectiles = [];
        this.particles = [];
        this.boss = null;
        this.canvas = null;
        this.ctx = null;
    }
}

// 游戏主类
class ThrowPoopGame {
    constructor() {
        this.state = new GameState();
        this.init();
    }

    init() {
        // 获取画布和上下文
        this.state.canvas = document.getElementById('gameCanvas');
        this.state.ctx = this.state.canvas.getContext('2d');
        
        // 初始化老板
        this.state.boss = new Boss(600, 200);
        
        // 绑定事件
        this.bindEvents();
        
        // 开始游戏循环
        this.gameLoop();
    }

    bindEvents() {
        // 鼠标事件
        this.state.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.state.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.state.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        
        // 触摸事件
        this.state.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.state.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
        this.state.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));
        
        // 按钮事件
        document.getElementById('startGameBtn').addEventListener('click', () => this.startGame());
        document.getElementById('resetGameBtn').addEventListener('click', () => this.resetGame());
        document.getElementById('rulesBtn').addEventListener('click', () => this.showRules());
        
        // 弹窗事件
        document.getElementById('closeModal').addEventListener('click', () => this.hideModal('resultModal'));
        document.getElementById('closeRulesModal').addEventListener('click', () => this.hideModal('rulesModal'));
        document.getElementById('newGameBtn').addEventListener('click', () => this.newGame());
        document.getElementById('continueGameBtn').addEventListener('click', () => this.continueGame());
        
        // 抽奖事件
        document.getElementById('drawLotteryBtn').addEventListener('click', () => this.drawLottery());
        
        // 广告事件
        document.getElementById('watchAdBtn').addEventListener('click', () => this.watchAd());
        document.getElementById('closeAdBtn').addEventListener('click', () => this.closeAd());
    }

    // 鼠标和触摸事件处理
    handleMouseDown(e) {
        if (!this.state.isPlaying || this.state.remainingChances <= 0) return;
        
        const rect = this.state.canvas.getBoundingClientRect();
        this.state.isDragging = true;
        this.state.dragStart = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
        
        this.showPowerMeter();
    }

    handleMouseMove(e) {
        if (!this.state.isDragging) return;
        
        const rect = this.state.canvas.getBoundingClientRect();
        this.state.dragEnd = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
        
        this.updatePowerMeter();
    }

    handleMouseUp(e) {
        if (!this.state.isDragging) return;
        
        this.state.isDragging = false;
        this.hidePowerMeter();
        this.throwProjectile();
    }

    handleTouchStart(e) {
        e.preventDefault();
        const touch = e.touches[0];
        this.handleMouseDown(touch);
    }

    handleTouchMove(e) {
        e.preventDefault();
        const touch = e.touches[0];
        this.handleMouseMove(touch);
    }

    handleTouchEnd(e) {
        e.preventDefault();
        this.handleMouseUp(e);
    }

    // 游戏控制方法
    startGame() {
        this.state.isPlaying = true;
        document.getElementById('startGameBtn').style.display = 'none';
        document.getElementById('resetGameBtn').style.display = 'inline-block';
        this.updateUI();
    }

    resetGame() {
        this.state.remainingChances = 3;
        this.state.hitCount = 0;
        this.state.projectiles = [];
        this.state.particles = [];
        this.state.boss = new Boss(600, 200);
        this.updateUI();
    }

    newGame() {
        this.hideModal('resultModal');
        this.resetGame();
    }

    continueGame() {
        this.hideModal('resultModal');
        this.state.isPlaying = true;
    }

    // 投掷机制
    throwProjectile() {
        if (this.state.remainingChances <= 0) return;
        
        const dx = this.state.dragEnd.x - this.state.dragStart.x;
        const dy = this.state.dragEnd.y - this.state.dragStart.y;
        
        const power = Math.min(Math.sqrt(dx * dx + dy * dy) / 5, 20);
        const angle = Math.atan2(dy, dx);
        
        const projectile = new Projectile(
            this.state.dragStart.x,
            this.state.dragStart.y,
            Math.cos(angle) * power,
            Math.sin(angle) * power
        );
        
        this.state.projectiles.push(projectile);
        this.state.remainingChances--;
        this.updateUI();
        
        // 播放投掷音效
        this.playSound('throwSound');
        
        // 检查游戏是否结束
        if (this.state.remainingChances <= 0) {
            setTimeout(() => this.checkGameEnd(), 3000);
        }
    }

    // 力度条控制
    showPowerMeter() {
        document.getElementById('powerMeterContainer').style.display = 'block';
    }

    hidePowerMeter() {
        document.getElementById('powerMeterContainer').style.display = 'none';
    }

    updatePowerMeter() {
        const dx = this.state.dragEnd.x - this.state.dragStart.x;
        const dy = this.state.dragEnd.y - this.state.dragStart.y;
        const power = Math.min(Math.sqrt(dx * dx + dy * dy) / 5, 20);
        const percentage = (power / 20) * 100;
        
        document.getElementById('powerBar').style.width = percentage + '%';
    }

    // 游戏循环
    gameLoop() {
        this.update();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }

    update() {
        if (!this.state.isPlaying) return;
        
        // 更新老板
        this.state.boss.update();
        
        // 更新投掷物
        this.state.projectiles.forEach((projectile, index) => {
            projectile.update();
            
            // 检查碰撞
            if (this.checkCollision(projectile, this.state.boss)) {
                this.handleHit(projectile);
                this.state.projectiles.splice(index, 1);
            }
            
            // 移除超出边界的投掷物
            if (projectile.x > this.state.canvas.width || 
                projectile.y > this.state.canvas.height ||
                projectile.x < 0) {
                this.state.projectiles.splice(index, 1);
            }
        });
        
        // 更新粒子效果
        this.state.particles.forEach((particle, index) => {
            particle.update();
            if (particle.life <= 0) {
                this.state.particles.splice(index, 1);
            }
        });
    }

    render() {
        // 清空画布
        this.state.ctx.clearRect(0, 0, this.state.canvas.width, this.state.canvas.height);
        
        // 绘制背景
        this.drawBackground();
        
        // 绘制老板
        this.state.boss.draw(this.state.ctx);
        
        // 绘制投掷物
        this.state.projectiles.forEach(projectile => {
            projectile.draw(this.state.ctx);
        });
        
        // 绘制粒子效果
        this.state.particles.forEach(particle => {
            particle.draw(this.state.ctx);
        });
        
        // 绘制瞄准线
        if (this.state.isDragging) {
            this.drawAimLine();
        }
    }

    drawBackground() {
        // 绘制天空和地面
        const gradient = this.state.ctx.createLinearGradient(0, 0, 0, this.state.canvas.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(0.7, '#87CEEB');
        gradient.addColorStop(0.7, '#90EE90');
        gradient.addColorStop(1, '#90EE90');
        
        this.state.ctx.fillStyle = gradient;
        this.state.ctx.fillRect(0, 0, this.state.canvas.width, this.state.canvas.height);
        
        // 绘制云朵
        this.drawClouds();
    }

    drawClouds() {
        this.state.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        
        // 云朵1
        this.drawCloud(100, 80, 60);
        this.drawCloud(300, 60, 80);
        this.drawCloud(500, 100, 70);
    }

    drawCloud(x, y, size) {
        this.state.ctx.beginPath();
        this.state.ctx.arc(x, y, size * 0.5, 0, Math.PI * 2);
        this.state.ctx.arc(x + size * 0.3, y, size * 0.7, 0, Math.PI * 2);
        this.state.ctx.arc(x + size * 0.6, y, size * 0.5, 0, Math.PI * 2);
        this.state.ctx.arc(x - size * 0.3, y, size * 0.6, 0, Math.PI * 2);
        this.state.ctx.fill();
    }

    drawAimLine() {
        this.state.ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
        this.state.ctx.lineWidth = 3;
        this.state.ctx.setLineDash([10, 5]);
        
        this.state.ctx.beginPath();
        this.state.ctx.moveTo(this.state.dragStart.x, this.state.dragStart.y);
        this.state.ctx.lineTo(this.state.dragEnd.x, this.state.dragEnd.y);
        this.state.ctx.stroke();
        
        this.state.ctx.setLineDash([]);
    }

    // 碰撞检测
    checkCollision(projectile, boss) {
        const dx = projectile.x - boss.x;
        const dy = projectile.y - boss.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        return distance < (projectile.radius + boss.radius);
    }

    // 命中处理
    handleHit(projectile) {
        this.state.hitCount++;
        this.updateUI();
        
        // 播放命中音效
        this.playSound('hitSound');
        
        // 创建粒子效果
        this.createHitParticles(projectile.x, projectile.y);
        
        // 老板受击动画
        this.state.boss.hit();
    }

    createHitParticles(x, y) {
        for (let i = 0; i < 10; i++) {
            const particle = new Particle(
                x,
                y,
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10,
                '#8B4513'
            );
            this.state.particles.push(particle);
        }
    }

    // 游戏结束检查
    checkGameEnd() {
        this.state.isPlaying = false;
        
        if (this.state.hitCount > 0) {
            this.showResultModal(true);
        } else {
            this.showResultModal(false);
        }
    }

    // 显示结果弹窗
    showResultModal(hasHits) {
        const modal = document.getElementById('resultModal');
        const resultTitle = document.getElementById('resultTitle');
        const resultMessage = document.getElementById('resultMessage');
        const hitResults = document.getElementById('hitResults');
        const lotterySection = document.getElementById('lotterySection');
        const adSection = document.getElementById('adSection');
        
        if (hasHits) {
            resultTitle.textContent = '🎉 恭喜命中！';
            resultMessage.textContent = `太棒了！你成功命中了 ${this.state.hitCount} 次！`;
            hitResults.innerHTML = `<div class="hit-stats">命中率: ${Math.round((this.state.hitCount / 3) * 100)}%</div>`;
            lotterySection.style.display = 'block';
            adSection.style.display = 'none';
        } else {
            resultTitle.textContent = '😅 再接再厉！';
            resultMessage.textContent = '这次没有命中，不要灰心！';
            hitResults.innerHTML = '';
            lotterySection.style.display = 'none';
            adSection.style.display = 'block';
        }
        
        modal.style.display = 'flex';
    }

    // 抽奖系统
    drawLottery() {
        const lotteryItems = document.querySelectorAll('.lottery-item');
        const drawBtn = document.getElementById('drawLotteryBtn');
        const prizeResult = document.getElementById('prizeResult');
        
        drawBtn.disabled = true;
        drawBtn.textContent = '抽奖中...';
        
        // 抽奖动画
        let spinCount = 0;
        const maxSpins = 20;
        
        const spinInterval = setInterval(() => {
            lotteryItems.forEach(item => item.classList.remove('spinning'));
            
            const randomIndex = Math.floor(Math.random() * lotteryItems.length);
            lotteryItems[randomIndex].classList.add('spinning');
            
            spinCount++;
            
            if (spinCount >= maxSpins) {
                clearInterval(spinInterval);
                this.finalizeLottery();
            }
        }, 100);
    }

    finalizeLottery() {
        const lotteryItems = document.querySelectorAll('.lottery-item');
        const prizeResult = document.getElementById('prizeResult');
        const continueBtn = document.getElementById('continueGameBtn');
        
        // 清除所有动画
        lotteryItems.forEach(item => {
            item.classList.remove('spinning');
            item.classList.remove('winner');
        });
        
        // 根据概率选择奖品
        const prizes = [
            { name: '全额退费', probability: 0.01, element: lotteryItems[0] },
            { name: '部分退费', probability: 0.05, element: lotteryItems[1] },
            { name: '红包1元', probability: 0.15, element: lotteryItems[2] },
            { name: '红包2元', probability: 0.15, element: lotteryItems[3] },
            { name: '额外机会', probability: 0.40, element: lotteryItems[4] },
            { name: '谢谢参与', probability: 0.24, element: lotteryItems[5] }
        ];
        
        const random = Math.random();
        let cumulativeProbability = 0;
        let selectedPrize = prizes[prizes.length - 1]; // 默认最后一个
        
        for (const prize of prizes) {
            cumulativeProbability += prize.probability;
            if (random <= cumulativeProbability) {
                selectedPrize = prize;
                break;
            }
        }
        
        // 显示中奖结果
        selectedPrize.element.classList.add('winner');
        prizeResult.innerHTML = `🎊 恭喜获得：<strong>${selectedPrize.name}</strong>！`;
        prizeResult.style.display = 'block';
        
        // 处理特殊奖品
        if (selectedPrize.name === '额外机会') {
            this.state.remainingChances += 3;
            continueBtn.style.display = 'inline-block';
            prizeResult.innerHTML += '<br>已为您增加3次投掷机会！';
        }
        
        // 重置抽奖按钮
        const drawBtn = document.getElementById('drawLotteryBtn');
        drawBtn.disabled = false;
        drawBtn.textContent = '立即抽奖';
    }

    // 广告系统
    watchAd() {
        const adModal = document.getElementById('adModal');
        const adCountdown = document.getElementById('adCountdown');
        const adProgressBar = document.getElementById('adProgressBar');
        const closeAdBtn = document.getElementById('closeAdBtn');
        
        adModal.style.display = 'flex';
        closeAdBtn.disabled = true;
        
        let countdown = 15;
        adCountdown.textContent = countdown;
        
        const adInterval = setInterval(() => {
            countdown--;
            adCountdown.textContent = countdown;
            
            const progress = ((15 - countdown) / 15) * 100;
            adProgressBar.style.width = progress + '%';
            
            if (countdown <= 0) {
                clearInterval(adInterval);
                closeAdBtn.disabled = false;
                closeAdBtn.textContent = '关闭广告 (获得3次机会)';
            }
        }, 1000);
    }

    closeAd() {
        const adModal = document.getElementById('adModal');
        const resultModal = document.getElementById('resultModal');
        const continueBtn = document.getElementById('continueGameBtn');
        
        // 增加机会
        this.state.remainingChances += 3;
        this.updateUI();
        
        // 关闭广告弹窗
        adModal.style.display = 'none';
        
        // 显示继续游戏按钮
        continueBtn.style.display = 'inline-block';
        
        // 重置广告状态
        document.getElementById('adCountdown').textContent = '15';
        document.getElementById('adProgressBar').style.width = '0%';
        document.getElementById('closeAdBtn').textContent = '关闭广告';
    }

    // 工具方法
    updateUI() {
        document.getElementById('remainingChances').textContent = this.state.remainingChances;
        document.getElementById('hitCount').textContent = this.state.hitCount;
    }

    showRules() {
        document.getElementById('rulesModal').style.display = 'flex';
    }

    hideModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    playSound(soundId) {
        const audio = document.getElementById(soundId);
        if (audio) {
            audio.currentTime = 0;
            audio.play().catch(() => {
                // 忽略音频播放错误
            });
        }
    }
}

// 老板类
class Boss {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.radius = 60;
        this.direction = 1;
        this.speed = 1;
        this.hitAnimation = 0;
        this.baseY = y;
    }

    update() {
        // 左右移动
        this.x += this.direction * this.speed;
        
        // 边界检测
        if (this.x <= this.radius + 50 || this.x >= 750 - this.radius) {
            this.direction *= -1;
        }
        
        // 轻微上下浮动
        this.y = this.baseY + Math.sin(Date.now() * 0.002) * 10;
        
        // 受击动画
        if (this.hitAnimation > 0) {
            this.hitAnimation--;
        }
    }

    draw(ctx) {
        ctx.save();
        
        // 受击效果
        if (this.hitAnimation > 0) {
            ctx.translate(Math.random() * 4 - 2, Math.random() * 4 - 2);
        }
        
        // 绘制老板身体
        ctx.fillStyle = '#FFE4B5';
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制脸部
        ctx.fillStyle = '#FFA07A';
        ctx.beginPath();
        ctx.arc(this.x, this.y - 10, this.radius * 0.8, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制眼睛
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.arc(this.x - 20, this.y - 20, 5, 0, Math.PI * 2);
        ctx.arc(this.x + 20, this.y - 20, 5, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制嘴巴
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(this.x, this.y, 15, 0, Math.PI);
        ctx.stroke();
        
        // 绘制帽子（代表老板身份）
        ctx.fillStyle = '#4169E1';
        ctx.fillRect(this.x - 30, this.y - 60, 60, 20);
        ctx.fillRect(this.x - 20, this.y - 80, 40, 20);
        
        // 绘制纸巾堆
        ctx.fillStyle = '#FFF';
        ctx.fillRect(this.x - 40, this.y + 40, 80, 30);
        ctx.strokeStyle = '#DDD';
        ctx.lineWidth = 2;
        ctx.strokeRect(this.x - 40, this.y + 40, 80, 30);
        
        // 绘制"云纸"标签
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('云纸', this.x, this.y + 60);
        
        ctx.restore();
    }

    hit() {
        this.hitAnimation = 30;
    }
}

// 投掷物类
class Projectile {
    constructor(x, y, vx, vy) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.radius = 15;
        this.gravity = 0.3;
        this.rotation = 0;
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += this.gravity;
        this.rotation += 0.2;
    }

    draw(ctx) {
        ctx.save();
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        
        // 绘制大粪
        ctx.fillStyle = '#8B4513';
        ctx.beginPath();
        ctx.arc(0, 0, this.radius, 0, Math.PI * 2);
        ctx.fill();
        
        // 添加纹理
        ctx.fillStyle = '#654321';
        ctx.beginPath();
        ctx.arc(-5, -5, 3, 0, Math.PI * 2);
        ctx.arc(5, 5, 4, 0, Math.PI * 2);
        ctx.arc(0, 8, 2, 0, Math.PI * 2);
        ctx.fill();
        
        // 添加臭气效果
        ctx.strokeStyle = 'rgba(255, 255, 0, 0.3)';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(0, -this.radius - 5, 3, 0, Math.PI * 2);
        ctx.arc(-8, -this.radius - 8, 2, 0, Math.PI * 2);
        ctx.arc(8, -this.radius - 6, 2, 0, Math.PI * 2);
        ctx.stroke();
        
        ctx.restore();
    }
}

// 粒子类
class Particle {
    constructor(x, y, vx, vy, color) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.color = color;
        this.life = 60;
        this.maxLife = 60;
        this.size = Math.random() * 5 + 2;
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += 0.1;
        this.vx *= 0.98;
        this.life--;
    }

    draw(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    new ThrowPoopGame();
}); 