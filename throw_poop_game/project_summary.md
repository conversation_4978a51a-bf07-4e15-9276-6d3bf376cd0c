# 丢粪大作战 项目摘要

## 🎮 项目信息
- **项目名称**: 丢粪大作战
- **副标题**: 扔得准，纸钱退！
- **项目类型**: HTML5 Canvas小游戏
- **开发语言**: HTML + CSS + JavaScript
- **目标平台**: Web浏览器（桌面端 + 移动端）

## 🎯 游戏概述
这是一个基于HTML5的投掷类小游戏，玩家通过滑动屏幕向卡通化的云纸老板投掷"大粪"来发泄情绪，同时获得抽奖机会。游戏设计幽默风趣，旨在为用户提供情绪宣泄渠道。

## 🕹️ 核心玩法
1. **投掷机制**: 玩家通过鼠标拖拽或触摸滑动控制投掷力度和方向
2. **目标移动**: 云纸老板会左右轻微晃动增加挑战性
3. **机会限制**: 玩家初始有3次免费投掷机会
4. **奖励系统**: 命中目标后进入抽奖环节
5. **广告机制**: 失败后可观看广告获得额外机会

## 🎨 美术风格
- **整体风格**: 卡通Q版，色彩明亮
- **老板形象**: 可爱搞笑的卡通老板形象
- **场景设计**: 简洁的背景，突出游戏主体
- **UI设计**: 现代化、直观的界面设计

## 🛠️ 技术实现
- **前端框架**: 原生HTML5 + Canvas
- **物理引擎**: 自定义轻量级物理引擎
- **动画系统**: requestAnimationFrame
- **响应式设计**: 适配不同屏幕尺寸
- **触摸支持**: 支持移动设备触摸操作

## 🎁 奖励机制
- **一等奖**: 全额退费（概率1%）
- **二等奖**: 部分退费券（概率5%）
- **三等奖**: 1-2元红包（概率30%）
- **四等奖**: 额外抽奖机会（概率40%）
- **谢谢参与**: 安慰奖（概率24%）

## 📱 用户体验
- **操作简单**: 一键上手，无需教程
- **反馈及时**: 投掷、命中、奖励都有清晰反馈
- **情绪宣泄**: 通过游戏操作有效释放负面情绪
- **成就感**: 通过技巧提升命中率获得满足感

## 🚀 后续扩展可能
- 增加不同难度模式
- 添加排行榜系统
- 引入更多投掷物品
- 节日主题活动
- 社交分享功能

## 📊 预期效果
- 提升用户情绪体验
- 增加广告观看率
- 提高用户粘性
- 为商业化提供数据支持 