# 🎯 丢粪大作战

一个基于HTML5 Canvas的趣味投掷小游戏，让用户通过投掷"大粪"向云纸老板发泄情绪，同时获得抽奖机会。

## 🎮 游戏特色

- **简单易上手**：拖拽鼠标或手指即可投掷
- **物理引擎**：真实的抛物线轨迹和重力效果
- **移动目标**：云纸老板会左右移动增加挑战性
- **抽奖系统**：命中目标后可参与抽奖获得奖品
- **广告机制**：失败后可观看广告获得额外机会
- **响应式设计**：完美适配桌面和移动设备

## 🚀 快速开始

### 方法一：直接运行（推荐）

1. 确保已安装Python 3
2. 在终端中进入游戏目录：
   ```bash
   cd throw_poop_game
   ```
3. 启动Web服务器：
   ```bash
   python3 -m http.server 8080
   ```
4. 在浏览器中访问：`http://localhost:8080`

### 方法二：使用其他Web服务器

如果你有其他Web服务器（如Apache、Nginx等），只需将游戏文件放在Web根目录下即可。

## 🎯 游戏玩法

1. **开始游戏**：点击"开始游戏"按钮
2. **瞄准投掷**：按住鼠标左键或手指拖拽来瞄准
3. **控制力度**：拖拽距离决定投掷力度
4. **释放投掷**：松开鼠标或手指完成投掷
5. **观察轨迹**：投掷物会按照物理轨迹飞行
6. **命中目标**：击中移动的云纸老板
7. **参与抽奖**：命中后可抽奖获得奖品
8. **观看广告**：失败后可看广告获得额外机会

## 🏆 奖品设置

- 🥇 **一等奖**：全额退费（概率1%）
- 🥈 **二等奖**：部分退费券（概率5%）
- 🥉 **三等奖**：1-2元红包（概率30%）
- 🎯 **四等奖**：额外抽奖机会（概率40%）
- 🤝 **参与奖**：谢谢参与（概率24%）

## 💡 游戏技巧

- 观察老板的移动规律，预判位置
- 控制投掷力度，避免过猛或过轻
- 瞄准老板的头部区域，命中率更高
- 利用抛物线轨迹计算落点
- 合理利用广告机会获得更多投掷次数

## 🛠️ 技术实现

- **前端技术**：HTML5 + CSS3 + JavaScript
- **图形渲染**：Canvas 2D API
- **物理引擎**：自定义轻量级物理引擎
- **动画系统**：requestAnimationFrame
- **响应式设计**：CSS媒体查询
- **触摸支持**：Touch Events API

## 📱 兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+
- ✅ iOS Safari 11+
- ✅ Android Chrome 60+

## 📂 文件结构

```
throw_poop_game/
├── index.html          # 游戏主页面
├── style.css           # 样式文件
├── game.js             # 游戏逻辑
├── todo.md             # 开发待办清单
├── project_summary.md  # 项目摘要
└── README.md           # 说明文档
```

## 🎨 自定义配置

你可以通过修改以下参数来调整游戏体验：

### 游戏难度调整（在game.js中）
```javascript
// 老板移动速度
this.speed = 1; // 默认值，可调整为0.5-2

// 投掷物重力
this.gravity = 0.3; // 默认值，可调整为0.1-0.5

// 碰撞检测范围
return distance < (projectile.radius + boss.radius); // 可调整半径
```

### 抽奖概率调整
```javascript
const prizes = [
    { name: '全额退费', probability: 0.01 }, // 1%
    { name: '部分退费', probability: 0.05 }, // 5%
    // ... 可自定义概率
];
```

## 🐛 问题排查

### 游戏无法加载
- 检查Web服务器是否正常启动
- 确认浏览器支持HTML5 Canvas
- 检查控制台是否有JavaScript错误

### 触摸操作无响应
- 确认设备支持触摸事件
- 检查是否有其他元素阻挡触摸
- 尝试刷新页面重新加载

### 音效无法播放
- 现代浏览器需要用户交互后才能播放音频
- 检查浏览器音频设置
- 音效文件为可选功能，不影响游戏核心玩法

## 📄 许可证

本项目仅供学习和演示使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进游戏！

---

**享受游戏，发泄情绪！** 🎯💩😄 