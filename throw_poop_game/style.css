/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', '微软雅黑', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

/* 游戏容器 */
.game-container {
    max-width: 900px;
    width: 100%;
    margin: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

/* 游戏标题 */
.game-header {
    text-align: center;
    padding: 20px;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
}

.game-header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 画布容器 */
.canvas-container {
    position: relative;
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
}

#gameCanvas {
    border: 3px solid #ddd;
    border-radius: 10px;
    background: linear-gradient(to bottom, #87CEEB 0%, #87CEEB 70%, #90EE90 70%, #90EE90 100%);
    cursor: pointer;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    height: auto;
}

/* 游戏UI覆盖层 */
.game-ui {
    position: absolute;
    top: 30px;
    left: 30px;
    right: 30px;
    pointer-events: none;
    z-index: 10;
}

.level-display,
.chances-display,
.hits-display {
    display: inline-block;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 15px;
    border-radius: 20px;
    margin: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-weight: bold;
}

.level-display {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
}

.label {
    color: #666;
    margin-right: 5px;
}

#remainingChances {
    color: #e74c3c;
    font-size: 1.2em;
}

#hitCount {
    color: #27ae60;
    font-size: 1.2em;
}

/* 力度条 */
.power-meter-container {
    position: absolute;
    bottom: 60px;
    left: 20px;
    text-align: center;
}

/* 角度显示 */
.angle-display {
    position: absolute;
    bottom: 60px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 15px;
    border-radius: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    pointer-events: none;
}

.angle-label {
    color: #333;
    font-weight: bold;
    font-size: 14px;
}

#angleValue {
    color: #e74c3c;
    font-size: 16px;
}

.power-meter {
    width: 200px;
    height: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid #fff;
}

.power-bar {
    height: 100%;
    background: linear-gradient(90deg, #2ecc71, #f1c40f, #e74c3c);
    width: 0%;
    transition: width 0.1s ease;
}

.power-label {
    display: block;
    margin-top: 5px;
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 游戏说明 */
.game-instructions {
    text-align: center;
    padding: 15px 20px;
    background: #e8f4fd;
    border-top: 1px solid #ddd;
}

.game-instructions p {
    color: #555;
    font-size: 1.1em;
}

/* 游戏控制按钮 */
.game-controls {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
}

.btn {
    padding: 12px 24px;
    margin: 0 10px;
    border: none;
    border-radius: 25px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #495057);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    background: linear-gradient(45deg, #5a6268, #3d4246);
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

.btn-info:hover {
    transform: translateY(-2px);
    background: linear-gradient(45deg, #138496, #0f6674);
}

.btn-lottery {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.btn-lottery:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6);
}

.btn-ad {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
}

.btn-ad:hover {
    transform: translateY(-2px);
    background: linear-gradient(45deg, #ee5a52, #dc3545);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    background: linear-gradient(45deg, #20c997, #17a2b8);
}

/* 弹窗样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    padding: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px 15px 0 0;
    position: relative;
}

.modal-header h2 {
    margin: 0;
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    cursor: pointer;
    color: white;
    opacity: 0.8;
}

.close:hover {
    opacity: 1;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
    text-align: center;
}

/* 抽奖轮盘 */
.lottery-wheel {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 20px 0;
}

.lottery-item {
    padding: 15px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border: 2px solid #dee2e6;
    border-radius: 10px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.lottery-item:hover {
    background: linear-gradient(45deg, #e9ecef, #dee2e6);
    transform: scale(1.05);
}

.lottery-item.spinning {
    animation: spin 0.1s ease-in-out;
}

.lottery-item.winner {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-color: #ffc107;
    transform: scale(1.1);
    animation: pulse 0.5s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1.1); }
    50% { transform: scale(1.15); }
}

/* 游戏规则样式 */
.rules-content h3 {
    color: #667eea;
    margin: 20px 0 10px 0;
    border-bottom: 2px solid #eee;
    padding-bottom: 5px;
}

.rules-content ul {
    list-style: none;
    padding-left: 0;
}

.rules-content li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.rules-content li:last-child {
    border-bottom: none;
}

/* 广告模拟样式 */
.ad-content {
    text-align: center;
    padding: 20px;
}

.ad-placeholder {
    background: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 40px 20px;
    margin: 20px 0;
}

.ad-timer {
    font-size: 1.5em;
    color: #e74c3c;
    margin: 20px 0;
    font-weight: bold;
}

.ad-progress {
    width: 100%;
    height: 10px;
    background: #eee;
    border-radius: 5px;
    overflow: hidden;
    margin: 20px 0;
}

.ad-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 1s linear;
}

#prizeResult {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    text-align: center;
    font-weight: bold;
    font-size: 1.2em;
    color: #333;
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
    animation: celebrate 0.5s ease-in-out;
}

@keyframes celebrate {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-container {
        margin: 10px;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
    
    #gameCanvas {
        width: 100%;
        height: auto;
    }
    
    .canvas-container {
        padding: 10px;
    }
    
    .game-ui {
        top: 20px;
        left: 20px;
        right: 20px;
    }
    
    .chances-display,
    .hits-display {
        display: block;
        margin: 5px auto;
        text-align: center;
    }
    
    .btn {
        display: block;
        margin: 10px auto;
        width: 200px;
    }
    
    .power-meter {
        width: 150px;
    }
    
    .lottery-wheel {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .modal-content {
        width: 95%;
        margin: 5px;
    }
}

@media (max-width: 480px) {
    .game-header h1 {
        font-size: 1.5em;
    }
    
    .subtitle {
        font-size: 1em;
    }
    
    .lottery-wheel {
        grid-template-columns: 1fr;
    }
    
    .power-meter {
        width: 120px;
    }
} 